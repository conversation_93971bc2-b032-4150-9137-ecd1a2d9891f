import { Controller, Get, Query, Req } from '@nestjs/common';
import { DashboardService } from './dashboard.service';

@Controller('api/dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('/stats')
  async getDashboardStats(@Req() req: Request) {
    const user = req['user'];
    const orgId = user.org_id;
    return this.dashboardService.getDashboardStats(orgId);
  }

  @Get('/recent-activity')
  async getRecentActivity(
    @Req() req: Request,
    @Query('limit') limit?: number
  ) {
    const user = req['user'];
    const orgId = user.org_id;
    return this.dashboardService.getRecentActivity(orgId, limit || 10);
  }

  @Get('/alerts-summary')
  async getAlertsSummary(@Req() req: Request) {
    const user = req['user'];
    const orgId = user.org_id;
    return this.dashboardService.getAlertsSummary(orgId);
  }

  @Get('/drones-status')
  async getDronesStatus(@Req() req: Request) {
    const user = req['user'];
    const orgId = user.org_id;
    return this.dashboardService.getDronesStatus(orgId);
  }
}
