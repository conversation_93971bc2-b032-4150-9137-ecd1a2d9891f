import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Constants from 'src/common/constants';

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);

  constructor(
    @InjectModel(Constants.drones) private readonly droneModel: Model<any>,
    @InjectModel(Constants.alertZones) private readonly alertZoneModel: Model<any>,
    @InjectModel(Constants.droneAuthorizations) private readonly droneAuthorizationModel: Model<any>,
    @InjectModel('logs') private readonly logModel: Model<any>,
  ) {}

  /**
   * Get dashboard statistics for an organization
   */
  async getDashboardStats(orgId: string) {
    try {
      const [
        totalDrones,
        activeDrones,
        totalAlertZones,
        activeAlertZones,
        totalAuthorizations,
        activeAuthorizations
      ] = await Promise.all([
        this.droneModel.countDocuments({ org_id: new Types.ObjectId(orgId), isDeleted: false }),
        this.droneModel.countDocuments({ org_id: new Types.ObjectId(orgId), isDeleted: false, isActive: true }),
        this.alertZoneModel.countDocuments({ org_id: new Types.ObjectId(orgId), isDeleted: false }),
        this.alertZoneModel.countDocuments({ org_id: new Types.ObjectId(orgId), isDeleted: false, isActive: true }),
        this.droneAuthorizationModel.countDocuments({ org_id: new Types.ObjectId(orgId), isDeleted: false }),
        this.droneAuthorizationModel.countDocuments({ org_id: new Types.ObjectId(orgId), isDeleted: false, isActive: true })
      ]);

      return {
        drones: {
          total: totalDrones,
          active: activeDrones,
          inactive: totalDrones - activeDrones
        },
        alertZones: {
          total: totalAlertZones,
          active: activeAlertZones,
          inactive: totalAlertZones - activeAlertZones
        },
        authorizations: {
          total: totalAuthorizations,
          active: activeAuthorizations,
          inactive: totalAuthorizations - activeAuthorizations
        }
      };
    } catch (error) {
      this.logger.error('Error getting dashboard stats:', error);
      throw error;
    }
  }

  /**
   * Get recent activity for an organization
   */
  async getRecentActivity(orgId: string, limit: number = 10) {
    try {
      const recentLogs = await this.logModel
        .find({ org_id: new Types.ObjectId(orgId) })
        .sort({ createdAt: -1 })
        .limit(limit)
        .populate('user_id', 'name email')
        .exec();

      return recentLogs;
    } catch (error) {
      this.logger.error('Error getting recent activity:', error);
      throw error;
    }
  }

  /**
   * Get alerts summary for an organization
   */
  async getAlertsSummary(orgId: string) {
    try {
      const alertZones = await this.alertZoneModel
        .find({ org_id: new Types.ObjectId(orgId), isDeleted: false })
        .select('name isActive alert_level last_triggered')
        .exec();

      const summary = {
        total: alertZones.length,
        active: alertZones.filter(zone => zone.isActive).length,
        byLevel: {
          low: alertZones.filter(zone => zone.alert_level === 'low').length,
          medium: alertZones.filter(zone => zone.alert_level === 'medium').length,
          high: alertZones.filter(zone => zone.alert_level === 'high').length,
          critical: alertZones.filter(zone => zone.alert_level === 'critical').length
        },
        recentlyTriggered: alertZones
          .filter(zone => zone.last_triggered)
          .sort((a, b) => new Date(b.last_triggered).getTime() - new Date(a.last_triggered).getTime())
          .slice(0, 5)
      };

      return summary;
    } catch (error) {
      this.logger.error('Error getting alerts summary:', error);
      throw error;
    }
  }

  /**
   * Get drones status for an organization
   */
  async getDronesStatus(orgId: string) {
    try {
      const drones = await this.droneModel
        .find({ org_id: new Types.ObjectId(orgId), isDeleted: false })
        .select('device_id name isActive last_seen status battery_level location')
        .exec();

      const dronesWithAuth = await Promise.all(
        drones.map(async (drone) => {
          const authorization = await this.droneAuthorizationModel
            .findOne({ 
              drone_id: drone._id, 
              isDeleted: false,
              isActive: true 
            })
            .select('expires_at')
            .exec();

          return {
            ...drone.toObject(),
            hasActiveAuthorization: !!authorization,
            authorizationExpiry: authorization?.expires_at || null
          };
        })
      );

      const summary = {
        total: drones.length,
        online: drones.filter(drone => drone.status === 'online').length,
        offline: drones.filter(drone => drone.status === 'offline').length,
        authorized: dronesWithAuth.filter(drone => drone.hasActiveAuthorization).length,
        lowBattery: drones.filter(drone => drone.battery_level && drone.battery_level < 20).length,
        drones: dronesWithAuth
      };

      return summary;
    } catch (error) {
      this.logger.error('Error getting drones status:', error);
      throw error;
    }
  }

  /**
   * Custom query method for running raw database queries
   */
  async runCustomQuery(collection: string, query: any, options: any = {}) {
    try {
      let model;
      
      switch (collection) {
        case 'drones':
          model = this.droneModel;
          break;
        case 'alertZones':
          model = this.alertZoneModel;
          break;
        case 'droneAuthorizations':
          model = this.droneAuthorizationModel;
          break;
        case 'logs':
          model = this.logModel;
          break;
        default:
          throw new Error(`Unsupported collection: ${collection}`);
      }

      const result = await model.find(query, null, options).exec();
      return result;
    } catch (error) {
      this.logger.error('Error running custom query:', error);
      throw error;
    }
  }

  /**
   * Run aggregation pipeline on a collection
   */
  async runAggregation(collection: string, pipeline: any[]) {
    try {
      let model;
      
      switch (collection) {
        case 'drones':
          model = this.droneModel;
          break;
        case 'alertZones':
          model = this.alertZoneModel;
          break;
        case 'droneAuthorizations':
          model = this.droneAuthorizationModel;
          break;
        case 'logs':
          model = this.logModel;
          break;
        default:
          throw new Error(`Unsupported collection: ${collection}`);
      }

      const result = await model.aggregate(pipeline).exec();
      return result;
    } catch (error) {
      this.logger.error('Error running aggregation:', error);
      throw error;
    }
  }
}
