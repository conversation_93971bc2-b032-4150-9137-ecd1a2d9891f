import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import DroneSchema from '../drones/drone.schema';
import alertZoneSchema from '../alertZones/alertZone.schema';
import DroneAuthorizationSchema from '../droneAuthorizations/droneAuthorization.schema';
import LogSchema from '../logging/log.schema';
import Constants from 'src/common/constants';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Constants.drones, schema: DroneSchema },
      { name: Constants.alertZones, schema: alertZoneSchema },
      { name: Constants.droneAuthorizations, schema: DroneAuthorizationSchema },
      { name: 'logs', schema: LogSchema },
    ]),
  ],
  controllers: [DashboardController],
  providers: [DashboardService],
  exports: [DashboardService],
})
export class DashboardModule {}
